"""Pandoc functionality tests"""

import tempfile
from pathlib import Path
from unittest.mock import Mock

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.pandoc_runner import PandocRunner
from app.core.database import Base
from app.models.template import TemplateResource, ResourceType


# Test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_pandoc.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="module")
def setup_database():
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session(setup_database):
    session = TestingSessionLocal()
    yield session
    session.close()


@pytest.fixture
def sample_markdown_file():
    """Create a sample markdown file for testing"""
    with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as f:
        f.write(
            "# Test Document\n\nThis is a **test** document with *emphasis*.\n\n- Item 1\n- Item 2\n"
        )
        temp_path = Path(f.name)

    yield temp_path

    # Cleanup
    if temp_path.exists():
        temp_path.unlink()


def test_pandoc_runner_initialization(db_session):
    """Test PandocRunner initialization"""
    runner = PandocRunner(db_session)
    assert runner.db == db_session
    assert runner.temp_dir is None


def test_get_extension():
    """Test file extension mapping"""
    runner = PandocRunner(Mock())

    assert runner._get_extension("html") == "html"
    assert runner._get_extension("pdf") == "pdf"
    assert runner._get_extension("docx") == "docx"
    assert runner._get_extension("unknown") == "out"


@pytest.mark.asyncio
async def test_get_resource_path_not_found(db_session):
    """Test getting resource path when resource doesn't exist"""
    from app.core.exception_handlers import TemplateNotFoundError

    runner = PandocRunner(db_session)

    with pytest.raises(TemplateNotFoundError) as exc_info:
        await runner._get_resource_path("nonexistent", ResourceType.DOCX_TEMPLATE)

    assert "nonexistent" in str(exc_info.value)
    assert "not found" in str(exc_info.value)


@pytest.mark.asyncio
async def test_get_resource_path_found(db_session):
    """Test getting resource path when resource exists"""
    # Create a temporary file
    with tempfile.NamedTemporaryFile(delete=False) as f:
        temp_path = Path(f.name)
        f.write(b"test content")

    try:
        # Create template resource in database
        template = TemplateResource(
            name="test_template",
            display_name="Test Template",
            resource_type=ResourceType.DOCX_TEMPLATE.value,
            file_path=str(temp_path),
            file_size=12,
            is_active=True,
        )
        db_session.add(template)
        db_session.commit()

        runner = PandocRunner(db_session)
        path = await runner._get_resource_path(
            "test_template", ResourceType.DOCX_TEMPLATE
        )

        assert path == temp_path.resolve()

    finally:
        # Cleanup
        if temp_path.exists():
            temp_path.unlink()


@pytest.mark.asyncio
async def test_build_command_basic(db_session, sample_markdown_file):
    """Test building basic Pandoc command"""
    runner = PandocRunner(db_session)
    runner.temp_dir = Path(tempfile.mkdtemp())

    try:
        cmd = await runner._build_command(
            input_file=sample_markdown_file,
            from_format="markdown",
            to_format="html",
            docx_template=None,
            csl_style=None,
            options=None,
            metadata=None,
            output_filename=None,
        )

        assert "pandoc" in cmd
        assert str(sample_markdown_file) in cmd
        assert "-f" in cmd
        assert "markdown" in cmd
        assert "-t" in cmd
        assert "html" in cmd
        assert "-o" in cmd

    finally:
        # Cleanup
        if runner.temp_dir and runner.temp_dir.exists():
            import shutil

            shutil.rmtree(runner.temp_dir)


@pytest.mark.asyncio
async def test_build_command_with_options(db_session, sample_markdown_file):
    """Test building Pandoc command with additional options"""
    runner = PandocRunner(db_session)
    runner.temp_dir = Path(tempfile.mkdtemp())

    try:
        options = {
            "standalone": True,
            "toc": True,
            "number-sections": True,
            "css": "style.css",
        }

        metadata = {"title": "Test Document", "author": "Test Author"}

        cmd = await runner._build_command(
            input_file=sample_markdown_file,
            from_format="markdown",
            to_format="html",
            docx_template=None,
            csl_style=None,
            options=options,
            metadata=metadata,
            output_filename="custom_output.html",
        )

        cmd_str = " ".join(cmd)
        assert "--standalone" in cmd_str
        assert "--toc" in cmd_str
        assert "--number-sections" in cmd_str
        assert "--css style.css" in cmd_str
        assert "-M title=Test Document" in cmd_str
        assert "-M author=Test Author" in cmd_str
        assert "custom_output.html" in cmd_str

    finally:
        # Cleanup
        if runner.temp_dir and runner.temp_dir.exists():
            import shutil

            shutil.rmtree(runner.temp_dir)
