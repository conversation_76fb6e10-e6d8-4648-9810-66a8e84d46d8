"""Application configuration"""

import re
from pathlib import Path
from typing import Optional, List, Union

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False
    )

    # Database
    database_url: str = Field(default="sqlite:///./pandoc_api.db")

    # Storage paths
    upload_dir: Path = Field(default=Path("storage/uploads"))
    output_dir: Path = Field(default=Path("storage/outputs"))
    template_dir: Path = Field(default=Path("templates"))

    # Additional template folders (comma-separated paths)
    additional_template_dirs: str = Field(default="")

    @field_validator("additional_template_dirs")
    @classmethod
    def validate_additional_template_dirs(cls, v: str) -> str:
        """Validate additional template directories"""
        if not v:
            return v

        # Split by comma and validate each path
        paths = [p.strip() for p in v.split(",") if p.strip()]
        for path_str in paths:
            path = Path(path_str)
            if not path.exists():
                raise ValueError(
                    f"Additional template directory does not exist: {path_str}"
                )

        return v

    @field_validator("max_file_size")
    @classmethod
    def validate_max_file_size(cls, v: Union[int, str]) -> int:
        """Parse max file size from string format (e.g., '100MB') to bytes"""
        if isinstance(v, int):
            return v

        if isinstance(v, str):
            # Parse human-readable format like "100MB", "1GB", etc.
            pattern = r"^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)$"
            match = re.match(pattern, v.upper().strip())

            if not match:
                raise ValueError(
                    f"Invalid file size format: {v}. Use format like '100MB', '1GB', etc."
                )

            size_value = float(match.group(1))
            unit = match.group(2)

            # Convert to bytes
            multipliers = {
                "B": 1,
                "KB": 1024,
                "MB": 1024**2,
                "GB": 1024**3,
                "TB": 1024**4,
            }

            return int(size_value * multipliers[unit])

        raise ValueError(f"max_file_size must be int or string, got {type(v)}")

    def get_additional_template_dirs(self) -> List[Path]:
        """Get list of additional template directories as Path objects"""
        if not self.additional_template_dirs:
            return []

        paths = [
            p.strip() for p in self.additional_template_dirs.split(",") if p.strip()
        ]
        return [Path(p) for p in paths]

    # API settings
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    debug: bool = Field(default=False)

    # Pandoc settings
    pandoc_timeout: int = Field(default=300)  # 5 minutes
    max_file_size: Union[int, str] = Field(default=100 * 1024 * 1024)  # 100MB

    # File expiration settings (in hours)
    upload_file_expiry_hours: int = Field(default=24, description="上传文件过期时间(小时) / Upload file expiry time in hours")
    output_file_expiry_hours: int = Field(default=72, description="输出文件过期时间(小时) / Output file expiry time in hours")
    template_file_expiry_hours: int = Field(default=0, description="模板文件过期时间(小时)，0表示永不过期 / Template file expiry time in hours, 0 means never expire")

    # File cleanup settings
    cleanup_interval_hours: int = Field(default=6, description="文件清理检查间隔(小时) / File cleanup check interval in hours")
    enable_auto_cleanup: bool = Field(default=True, description="是否启用自动文件清理 / Whether to enable automatic file cleanup")

    # Security
    secret_key: str = Field(default="your-secret-key-here")

    # Logging settings
    log_level: str = Field(default="INFO")
    log_format: str = Field(default="rich")  # "rich", "json", or "standard"
    log_file: Optional[str] = Field(default=None)  # Optional log file path

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure directories exist
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.template_dir.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()
