{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.2", "globals": "149784dfaca0993d6c7dd3613fba2f72", "files": {"z_cfb6adc3f81c8e3c___init___py": {"hash": "f3d5873f4a83d5d0d6b7d6a5113f0456", "index": {"url": "z_cfb6adc3f81c8e3c___init___py.html", "file": "app/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c_pandoc_py": {"hash": "95ef08035827a7a244bca895d5952f6c", "index": {"url": "z_cfb6adc3f81c8e3c_pandoc_py.html", "file": "app/api/pandoc.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c_templates_py": {"hash": "37e6110ae29e3c419fb94e505dfcea4f", "index": {"url": "z_cfb6adc3f81c8e3c_templates_py.html", "file": "app/api/templates.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 109, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417___init___py": {"hash": "c0ce464de06a58fb6b208d72c65c1e8c", "index": {"url": "z_8f7e1016f2d37417___init___py.html", "file": "app/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_config_py": {"hash": "1d6c319f91c19b99760e916a11ef20d3", "index": {"url": "z_8f7e1016f2d37417_config_py.html", "file": "app/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 63, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_database_py": {"hash": "deb3a9c1f179e1ece2584f21589f24ec", "index": {"url": "z_8f7e1016f2d37417_database_py.html", "file": "app/core/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_exception_handlers_py": {"hash": "3672a740f67e27d26da51a01b68eb79a", "index": {"url": "z_8f7e1016f2d37417_exception_handlers_py.html", "file": "app/core/exception_handlers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_http_logging_py": {"hash": "198ee539b0d3f4c36144b359afc396c2", "index": {"url": "z_8f7e1016f2d37417_http_logging_py.html", "file": "app/core/http_logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_logging_config_py": {"hash": "133c3a5dd0b359438d749ebec0a15e5d", "index": {"url": "z_8f7e1016f2d37417_logging_config_py.html", "file": "app/core/logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_pandoc_runner_py": {"hash": "2759c62142c5ffa9cdc4786079eaef35", "index": {"url": "z_8f7e1016f2d37417_pandoc_runner_py.html", "file": "app/core/pandoc_runner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 28, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_template_manager_py": {"hash": "eab1c014f88e3095a376a60d39db68d2", "index": {"url": "z_8f7e1016f2d37417_template_manager_py.html", "file": "app/core/template_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b___init___py": {"hash": "563572dc73d59816616135123e0590b2", "index": {"url": "z_6c0e4b930745278b___init___py.html", "file": "app/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_file_record_py": {"hash": "fa6a10b97f810ee1d3378070c276f03d", "index": {"url": "z_6c0e4b930745278b_file_record_py.html", "file": "app/models/file_record.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 2, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_template_py": {"hash": "69c6b4c2233af323b5396ec9d1813754", "index": {"url": "z_6c0e4b930745278b_template_py.html", "file": "app/models/template.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 2, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c___init___py": {"hash": "7a767b6761aea47a23af09040a749438", "index": {"url": "z_c0f67d75e686303c___init___py.html", "file": "app/schemas/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_pandoc_py": {"hash": "5e5c9729e5dde730f61cdd0d124fe83c", "index": {"url": "z_c0f67d75e686303c_pandoc_py.html", "file": "app/schemas/pandoc.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c0f67d75e686303c_template_py": {"hash": "9d6d8fee06ef86be264302368ade5270", "index": {"url": "z_c0f67d75e686303c_template_py.html", "file": "app/schemas/template.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69___init___py": {"hash": "9f3f030bbecce7ba66d8dbbfa8a52e01", "index": {"url": "z_c318f3fa19a49f69___init___py.html", "file": "app/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c318f3fa19a49f69_file_cleanup_py": {"hash": "86d45ec5484f46e05d184a615669c76d", "index": {"url": "z_c318f3fa19a49f69_file_cleanup_py.html", "file": "app/services/file_cleanup.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16___init___py": {"hash": "fcd075b355f660b7a7e912e157abf567", "index": {"url": "z_748a0465d46c2a16___init___py.html", "file": "app/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16_file_handler_py": {"hash": "0fe5fc48d75a1d7a89aa81cc4f7290ad", "index": {"url": "z_748a0465d46c2a16_file_handler_py.html", "file": "app/utils/file_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_748a0465d46c2a16_zip_handler_py": {"hash": "db790d71128432faa7f1359679b498c0", "index": {"url": "z_748a0465d46c2a16_zip_handler_py.html", "file": "app/utils/zip_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 66, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}