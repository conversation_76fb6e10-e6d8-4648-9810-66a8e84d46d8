"""Tests for refactored utility classes with exception handling"""

import tempfile
import zipfile
from pathlib import Path
from unittest.mock import patch, Mo<PERSON>
import pytest

from app.utils.file_handler import FileHandler
from app.utils.zip_handler import ZipHandler
from app.core.exception_handlers import FileProcessingError


class TestFileHandlerExceptions:
    """Test exception handling in FileHandler"""

    def test_delete_file_success(self):
        """Test successful file deletion"""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as f:
            temp_path = Path(f.name)
            f.write(b"test content")

        # File should exist
        assert temp_path.exists()

        # Delete should succeed without raising exception
        FileHandler.delete_file(temp_path)

        # File should be gone
        assert not temp_path.exists()

    def test_delete_file_nonexistent(self):
        """Test deleting nonexistent file doesn't raise exception"""
        nonexistent_path = Path("/nonexistent/file.txt")
        
        # Should not raise exception for nonexistent file
        FileHandler.delete_file(nonexistent_path)

    def test_delete_file_permission_error(self):
        """Test file deletion with permission error raises FileProcessingError"""
        temp_path = Path("/tmp/test_file.txt")

        with patch('pathlib.Path.unlink') as mock_unlink:
            mock_unlink.side_effect = PermissionError("Permission denied")

            with patch('pathlib.Path.exists', return_value=True):
                with pytest.raises(FileProcessingError) as exc_info:
                    FileHandler.delete_file(temp_path)

                assert "Failed to delete file" in str(exc_info.value)
                assert str(temp_path) in str(exc_info.value)
                assert "Permission denied" in exc_info.value.details["error"]

    def test_delete_file_os_error(self):
        """Test file deletion with OS error raises FileProcessingError"""
        temp_path = Path("/tmp/test_file.txt")

        with patch('pathlib.Path.unlink') as mock_unlink:
            mock_unlink.side_effect = OSError("Device busy")

            with patch('pathlib.Path.exists', return_value=True):
                with pytest.raises(FileProcessingError) as exc_info:
                    FileHandler.delete_file(temp_path)

                assert "Failed to delete file" in str(exc_info.value)
                assert "Device busy" in exc_info.value.details["error"]

    def test_cleanup_extraction_success(self):
        """Test successful extraction directory cleanup"""
        # Create a temporary directory
        temp_dir = Path(tempfile.mkdtemp())
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")

        # Directory should exist
        assert temp_dir.exists()
        assert test_file.exists()

        # Cleanup should succeed
        FileHandler.cleanup_extraction(temp_dir)

        # Directory should be gone
        assert not temp_dir.exists()

    def test_cleanup_extraction_none(self):
        """Test cleanup with None directory doesn't raise exception"""
        # Should not raise exception
        FileHandler.cleanup_extraction(None)

    def test_cleanup_extraction_nonexistent(self):
        """Test cleanup of nonexistent directory doesn't raise exception"""
        nonexistent_dir = Path("/nonexistent/directory")
        
        # Should not raise exception
        FileHandler.cleanup_extraction(nonexistent_dir)


class TestZipHandlerExceptions:
    """Test exception handling in ZipHandler"""

    def test_cleanup_extraction_success(self):
        """Test successful extraction directory cleanup"""
        # Create a temporary directory with files
        temp_dir = Path(tempfile.mkdtemp())
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")
        
        subdir = temp_dir / "subdir"
        subdir.mkdir()
        (subdir / "nested.txt").write_text("nested content")

        # Directory should exist with content
        assert temp_dir.exists()
        assert test_file.exists()
        assert subdir.exists()

        # Cleanup should succeed
        ZipHandler.cleanup_extraction(temp_dir)

        # Directory should be completely gone
        assert not temp_dir.exists()

    def test_cleanup_extraction_nonexistent(self):
        """Test cleanup of nonexistent directory doesn't raise exception"""
        nonexistent_dir = Path("/nonexistent/directory")
        
        # Should not raise exception for nonexistent directory
        ZipHandler.cleanup_extraction(nonexistent_dir)

    def test_cleanup_extraction_permission_error(self):
        """Test cleanup with permission error raises FileProcessingError"""
        temp_dir = Path("/tmp/test_extract_dir")

        with patch('shutil.rmtree') as mock_rmtree:
            mock_rmtree.side_effect = PermissionError("Permission denied")

            with patch('pathlib.Path.exists', return_value=True):
                with pytest.raises(FileProcessingError) as exc_info:
                    ZipHandler.cleanup_extraction(temp_dir)

                assert "Failed to cleanup extraction directory" in str(exc_info.value)
                assert str(temp_dir) in str(exc_info.value)
                assert "Permission denied" in exc_info.value.details["error"]

    def test_cleanup_extraction_os_error(self):
        """Test cleanup with OS error raises FileProcessingError"""
        temp_dir = Path("/tmp/test_extract_dir")

        with patch('shutil.rmtree') as mock_rmtree:
            mock_rmtree.side_effect = OSError("Device busy")

            with patch('pathlib.Path.exists', return_value=True):
                with pytest.raises(FileProcessingError) as exc_info:
                    ZipHandler.cleanup_extraction(temp_dir)

                assert "Failed to cleanup extraction directory" in str(exc_info.value)
                assert "Device busy" in exc_info.value.details["error"]

    def test_extract_zip_success(self):
        """Test successful ZIP extraction"""
        # Create a test ZIP file
        with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as zip_file:
            zip_path = Path(zip_file.name)

        # Create ZIP with markdown content
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            zipf.writestr("README.md", "# Test Document\n\nThis is a test.")
            zipf.writestr("other.txt", "Other content")

        try:
            # Extract should succeed
            extract_dir, main_file = ZipHandler.extract_zip(zip_path)

            # Should return valid paths
            assert extract_dir.exists()
            assert main_file.exists()
            assert main_file.name == "README.md"
            assert main_file.read_text() == "# Test Document\n\nThis is a test."

            # Cleanup
            ZipHandler.cleanup_extraction(extract_dir)

        finally:
            # Cleanup ZIP file
            if zip_path.exists():
                zip_path.unlink()

    def test_extract_zip_bad_zip_file(self):
        """Test extraction of corrupted ZIP file raises BadZipFile"""
        # Create a file that's not a valid ZIP
        with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as bad_zip:
            bad_zip.write(b"This is not a ZIP file")
            bad_zip_path = Path(bad_zip.name)

        try:
            with pytest.raises(zipfile.BadZipFile):
                ZipHandler.extract_zip(bad_zip_path)

        finally:
            if bad_zip_path.exists():
                bad_zip_path.unlink()

    def test_extract_zip_no_main_document(self):
        """Test extraction of ZIP with no suitable main document raises ValueError"""
        # Create a ZIP with no markdown files
        with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as zip_file:
            zip_path = Path(zip_file.name)

        with zipfile.ZipFile(zip_path, 'w') as zipf:
            zipf.writestr("image.jpg", b"fake image data")
            zipf.writestr("data.csv", "col1,col2\nval1,val2")

        try:
            with pytest.raises(ValueError) as exc_info:
                ZipHandler.extract_zip(zip_path)

            assert "No suitable main document file found" in str(exc_info.value)

        finally:
            if zip_path.exists():
                zip_path.unlink()

    def test_is_zip_file_valid(self):
        """Test is_zip_file with valid ZIP"""
        # Create a valid ZIP file
        with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as zip_file:
            zip_path = Path(zip_file.name)

        with zipfile.ZipFile(zip_path, 'w') as zipf:
            zipf.writestr("test.txt", "test content")

        try:
            assert ZipHandler.is_zip_file(zip_path) is True
        finally:
            if zip_path.exists():
                zip_path.unlink()

    def test_is_zip_file_invalid(self):
        """Test is_zip_file with invalid file"""
        # Create a non-ZIP file
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as text_file:
            text_file.write(b"This is not a ZIP file")
            text_path = Path(text_file.name)

        try:
            assert ZipHandler.is_zip_file(text_path) is False
        finally:
            if text_path.exists():
                text_path.unlink()

    def test_is_zip_file_nonexistent(self):
        """Test is_zip_file with nonexistent file"""
        nonexistent_path = Path("/nonexistent/file.zip")
        assert ZipHandler.is_zip_file(nonexistent_path) is False
