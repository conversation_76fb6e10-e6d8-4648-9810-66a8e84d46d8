<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">59%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.2">coverage.py v7.10.2</a>,
            created at 2025-08-05 14:47 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html#t39">app/api/pandoc.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html#t39"><data value='convert_document'>convert_document</data></a></td>
                <td>71</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="35 71">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html#t247">app/api/pandoc.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html#t247"><data value='download_file'>download_file</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html#t299">app/api/pandoc.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html#t299"><data value='get_supported_formats'>get_supported_formats</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html#t383">app/api/pandoc.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html#t383"><data value='cleanup_expired_files'>cleanup_expired_files</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html">app/api/pandoc.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t39">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t39"><data value='upload_template'>upload_template</data></a></td>
                <td>23</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="16 23">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t134">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t134"><data value='list_templates'>list_templates</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t183">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t183"><data value='get_template'>get_template</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t210">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t210"><data value='update_template'>update_template</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t252">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t252"><data value='delete_template'>delete_template</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t292">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t292"><data value='get_resource_types'>get_resource_types</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t325">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t325"><data value='reload_templates'>reload_templates</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t361">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t361"><data value='search_templates'>search_templates</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t401">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html#t401"><data value='list_builtin_templates'>list_builtin_templates</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t31">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t31"><data value='validate_additional_template_dirs'>Settings.validate_additional_template_dirs</data></a></td>
                <td>8</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="2 8">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t49">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t49"><data value='validate_max_file_size'>Settings.validate_max_file_size</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t80">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t80"><data value='get_additional_template_dirs'>Settings.get_additional_template_dirs</data></a></td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t116">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t116"><data value='init__'>Settings.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t23">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t23"><data value='get_db'>get_db</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t32">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t32"><data value='create_tables'>create_tables</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t20">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t20"><data value='init__'>PandocAPIException.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t35">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t35"><data value='init__'>PandocConversionError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t46">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t46"><data value='init__'>TemplateNotFoundError.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t58">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t58"><data value='init__'>FileProcessingError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t72">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t72"><data value='init__'>ValidationError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t83">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t83"><data value='pandoc_api_exception_handler'>pandoc_api_exception_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t106">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t106"><data value='http_exception_handler'>http_exception_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t127">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t127"><data value='general_exception_handler'>general_exception_handler</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t158">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t158"><data value='validation_exception_handler'>validation_exception_handler</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t184">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t184"><data value='format_exception_for_console'>format_exception_for_console</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t204">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t204"><data value='setup_exception_handlers'>setup_exception_handlers</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t15">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t15"><data value='init__'>HTTPLoggingMiddleware.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t19">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t19"><data value='dispatch'>HTTPLoggingMiddleware.dispatch</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t54">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t54"><data value='get_client_ip'>HTTPLoggingMiddleware.get_client_ip</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t71">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t71"><data value='log_request'>HTTPLoggingMiddleware.log_request</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t100">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t100"><data value='log_response'>HTTPLoggingMiddleware.log_response</data></a></td>
                <td>25</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="14 25">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t155">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t155"><data value='log_error'>HTTPLoggingMiddleware.log_error</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t183">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t183"><data value='setup_http_logging'>setup_http_logging</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t191">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t191"><data value='configure_uvicorn_loggers'>configure_uvicorn_loggers</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t15">app/core/logging_config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t15"><data value='setup_logging'>setup_logging</data></a></td>
                <td>54</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="25 54">46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t159">app/core/logging_config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t159"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t164">app/core/logging_config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t164"><data value='log_exception'>log_exception</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t179">app/core/logging_config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t179"><data value='log_success'>log_success</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t193">app/core/logging_config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t193"><data value='log_warning'>log_warning</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t207">app/core/logging_config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html#t207"><data value='log_error'>log_error</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html">app/core/logging_config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t26">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t26"><data value='init__'>PandocRunner.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t30">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t30"><data value='convert'>PandocRunner.convert</data></a></td>
                <td>20</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="14 20">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t120">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t120"><data value='build_command'>PandocRunner._build_command</data></a></td>
                <td>33</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="21 33">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t188">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t188"><data value='get_resource_path'>PandocRunner._get_resource_path</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t218">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t218"><data value='execute_pandoc'>PandocRunner._execute_pandoc</data></a></td>
                <td>12</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="4 12">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t266">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t266"><data value='find_output_files'>PandocRunner._find_output_files</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t278">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t278"><data value='move_output_files'>PandocRunner._move_output_files</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t294">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t294"><data value='get_extension'>PandocRunner._get_extension</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t20">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t20"><data value='init__'>TemplateMetadata.__init__</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t40">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t40"><data value='to_dict'>TemplateMetadata.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t54">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t54"><data value='from_dict'>TemplateMetadata.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t71">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t71"><data value='init__'>TemplateManager.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t82">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t82"><data value='scan_and_load_templates'>TemplateManager.scan_and_load_templates</data></a></td>
                <td>16</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="11 16">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t125">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t125"><data value='scan_directory'>TemplateManager._scan_directory</data></a></td>
                <td>16</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="9 16">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t158">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t158"><data value='load_template_file'>TemplateManager._load_template_file</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t219">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t219"><data value='load_template_metadata'>TemplateManager._load_template_metadata</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t240">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t240"><data value='update_existing_template'>TemplateManager._update_existing_template</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t255">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t255"><data value='reload_templates'>TemplateManager.reload_templates</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t260">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t260"><data value='get_template_by_name'>TemplateManager.get_template_by_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t268">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t268"><data value='get_templates_by_type'>TemplateManager.get_templates_by_type</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t281">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t281"><data value='search_templates'>TemplateManager.search_templates</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t56">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t56"><data value='repr__'>FileRecord.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t60">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t60"><data value='file_exists'>FileRecord.file_exists</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t65">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t65"><data value='is_expired'>FileRecord.is_expired</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t72">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t72"><data value='time_until_expiry'>FileRecord.time_until_expiry</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t78">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t78"><data value='get_absolute_path'>FileRecord.get_absolute_path</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t82">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t82"><data value='mark_accessed'>FileRecord.mark_accessed</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t86">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t86"><data value='mark_deleted'>FileRecord.mark_deleted</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t92">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t92"><data value='calculate_expiry_time'>FileRecord.calculate_expiry_time</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t109">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t109"><data value='create_record'>FileRecord.create_record</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t46">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t46"><data value='repr__'>TemplateResource.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t50">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t50"><data value='file_exists'>TemplateResource.file_exists</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t54">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t54"><data value='get_absolute_path'>TemplateResource.get_absolute_path</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t58">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t58"><data value='get_metadata'>TemplateResource.get_metadata</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t62">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t62"><data value='set_metadata'>TemplateResource.set_metadata</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t66">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t66"><data value='update_metadata'>TemplateResource.update_metadata</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html">app/schemas/__init__.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html">app/schemas/pandoc.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>55</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="55 55">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t23">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t23"><data value='init__'>FileCleanupService.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t27">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t27"><data value='start'>FileCleanupService.start</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t41">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t41"><data value='stop'>FileCleanupService.stop</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t56">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t56"><data value='cleanup_loop'>FileCleanupService._cleanup_loop</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t70">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t70"><data value='cleanup_expired_files'>FileCleanupService.cleanup_expired_files</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t117">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t117"><data value='get_expired_files'>FileCleanupService._get_expired_files</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t131">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t131"><data value='cleanup_file'>FileCleanupService._cleanup_file</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t162">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t162"><data value='cleanup_orphaned_files'>FileCleanupService._cleanup_orphaned_files</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t195">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t195"><data value='cleanup_orphaned_in_directory'>FileCleanupService._cleanup_orphaned_in_directory</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t256">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t256"><data value='start_cleanup_service'>start_cleanup_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t261">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t261"><data value='stop_cleanup_service'>stop_cleanup_service</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t266">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t266"><data value='manual_cleanup'>manual_cleanup</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16___init___py.html">app/utils/__init__.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t22">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t22"><data value='save_upload_file'>FileHandler.save_upload_file</data></a></td>
                <td>22</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="19 22">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t94">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t94"><data value='record_output_file'>FileHandler.record_output_file</data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t142">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t142"><data value='save_template_file'>FileHandler.save_template_file</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t194">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t194"><data value='get_mime_type'>FileHandler.get_mime_type</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t200">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t200"><data value='get_file_size'>FileHandler.get_file_size</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t205">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t205"><data value='delete_file'>FileHandler.delete_file</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t224">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t224"><data value='validate_file_extension'>FileHandler.validate_file_extension</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t233">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t233"><data value='save_and_process_upload_file'>FileHandler.save_and_process_upload_file</data></a></td>
                <td>34</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="3 34">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t316">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t316"><data value='cleanup_extraction'>FileHandler.cleanup_extraction</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t331">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t331"><data value='is_zip_file'>FileHandler.is_zip_file</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t14">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t14"><data value='create_zip'>ZipHandler.create_zip</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t42">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t42"><data value='should_zip_files'>ZipHandler.should_zip_files</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t59">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t59"><data value='extract_zip'>ZipHandler.extract_zip</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t94">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t94"><data value='find_main_document'>ZipHandler._find_main_document</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t136">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t136"><data value='has_priority_name'>ZipHandler._find_main_document.has_priority_name</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t141">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t141"><data value='get_file_depth'>ZipHandler._find_main_document.get_file_depth</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t145">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t145"><data value='sort_key'>ZipHandler._find_main_document.sort_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t170">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t170"><data value='is_zip_file'>ZipHandler.is_zip_file</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t189">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t189"><data value='cleanup_extraction'>ZipHandler.cleanup_extraction</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1344</td>
                <td>557</td>
                <td>4</td>
                <td class="right" data-ratio="787 1344">59%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.2">coverage.py v7.10.2</a>,
            created at 2025-08-05 14:47 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
