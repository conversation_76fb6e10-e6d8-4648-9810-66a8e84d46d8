<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">59%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.2">coverage.py v7.10.2</a>,
            created at 2025-08-05 14:47 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html">app/api/pandoc.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_pandoc_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>125</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="66 125">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html">app/api/templates.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c_templates_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>109</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="61 109">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t11">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t11"><data value='Settings'>Settings</data></a></td>
                <td>28</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="17 28">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="8 13">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t17">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t17"><data value='PandocAPIException'>PandocAPIException</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t32">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t32"><data value='PandocConversionError'>PandocConversionError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t43">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t43"><data value='TemplateNotFoundError'>TemplateNotFoundError</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t55">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t55"><data value='FileProcessingError'>FileProcessingError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t69">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html#t69"><data value='ValidationError'>ValidationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html">app/core/exception_handlers.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exception_handlers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="32 54">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t12">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html#t12"><data value='HTTPLoggingMiddleware'>HTTPLoggingMiddleware</data></a></td>
                <td>61</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="38 61">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html">app/core/http_logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_http_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="27 30">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html">app/core/logging_config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="42 81">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t23">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html#t23"><data value='PandocRunner'>PandocRunner</data></a></td>
                <td>90</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="62 90">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html">app/core/pandoc_runner.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_pandoc_runner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t17">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t17"><data value='TemplateMetadata'>TemplateMetadata</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t68">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html#t68"><data value='TemplateManager'>TemplateManager</data></a></td>
                <td>92</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="29 92">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html">app/core/template_manager.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_template_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t15">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t15"><data value='FileType'>FileType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t27">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html#t27"><data value='FileRecord'>FileRecord</data></a></td>
                <td>24</td>
                <td>15</td>
                <td>1</td>
                <td class="right" data-ratio="9 24">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html">app/models/file_record.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_file_record_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t14">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t14"><data value='ResourceType'>ResourceType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t27">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html#t27"><data value='TemplateResource'>TemplateResource</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>1</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html">app/models/template.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_template_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html">app/schemas/__init__.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t8">app/schemas/pandoc.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t8"><data value='PandocConversionRequest'>PandocConversionRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t55">app/schemas/pandoc.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t55"><data value='FileInfo'>FileInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t70">app/schemas/pandoc.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t70"><data value='PandocConversionResponse'>PandocConversionResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t99">app/schemas/pandoc.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t99"><data value='PandocError'>PandocError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t119">app/schemas/pandoc.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html#t119"><data value='ConversionStatus'>ConversionStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html">app/schemas/pandoc.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_pandoc_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t11">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t11"><data value='TemplateMetadataSchema'>TemplateMetadataSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t32">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t32"><data value='TemplateResourceBase'>TemplateResourceBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t56">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t56"><data value='TemplateResourceCreate'>TemplateResourceCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t66">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t66"><data value='TemplateResourceUpdate'>TemplateResourceUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t83">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t83"><data value='TemplateResourceResponse'>TemplateResourceResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t102">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t102"><data value='TemplateResourceList'>TemplateResourceList</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t117">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t117"><data value='TemplateReloadResponse'>TemplateReloadResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t138">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html#t138"><data value='TemplateSearchRequest'>TemplateSearchRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html">app/schemas/template.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_template_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="55 55">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t20">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html#t20"><data value='FileCleanupService'>FileCleanupService</data></a></td>
                <td>101</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="2 101">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html">app/services/file_cleanup.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_file_cleanup_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="25 28">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16___init___py.html">app/utils/__init__.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t18">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html#t18"><data value='FileHandler'>FileHandler</data></a></td>
                <td>101</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="52 101">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html">app/utils/file_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_file_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t10">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html#t10"><data value='ZipHandler'>ZipHandler</data></a></td>
                <td>70</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="4 70">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html">app/utils/zip_handler.py</a></td>
                <td class="name left"><a href="z_748a0465d46c2a16_zip_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1344</td>
                <td>557</td>
                <td>4</td>
                <td class="right" data-ratio="787 1344">59%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.2">coverage.py v7.10.2</a>,
            created at 2025-08-05 14:47 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
