"""Tests for refactored Pandoc<PERSON><PERSON>ner with exception handling"""

import tempfile
import asyncio
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.pandoc_runner import <PERSON>doc<PERSON><PERSON>ner
from app.core.database import Base
from app.models.template import TemplateResource, ResourceType
from app.core.exception_handlers import (
    PandocConversionError,
    FileProcessingError,
    TemplateNotFoundError,
)


# Test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_pandoc_exceptions.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="module")
def setup_database():
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session(setup_database):
    session = TestingSessionLocal()
    yield session
    session.close()


@pytest.fixture
def sample_markdown_file():
    """Create a sample markdown file for testing"""
    with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as f:
        f.write(
            "# Test Document\n\nThis is a **test** document with *emphasis*.\n\n- Item 1\n- Item 2\n"
        )
        temp_path = Path(f.name)

    yield temp_path

    # Cleanup
    if temp_path.exists():
        temp_path.unlink()


@pytest.fixture
def runner_with_temp_dir(db_session):
    """Create a PandocRunner with a temporary directory"""
    runner = PandocRunner(db_session)
    runner.temp_dir = Path(tempfile.mkdtemp())
    yield runner
    
    # Cleanup
    if runner.temp_dir and runner.temp_dir.exists():
        import shutil
        shutil.rmtree(runner.temp_dir)


class TestPandocRunnerExceptions:
    """Test exception handling in PandocRunner"""

    @pytest.mark.asyncio
    async def test_convert_nonexistent_file(self, db_session):
        """Test conversion with nonexistent input file raises FileProcessingError"""
        runner = PandocRunner(db_session)
        nonexistent_file = Path("/nonexistent/file.md")

        with pytest.raises(FileProcessingError) as exc_info:
            await runner.convert(
                input_file=nonexistent_file,
                from_format="markdown",
                to_format="html"
            )

        assert "does not exist" in str(exc_info.value)
        assert str(nonexistent_file) in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_convert_file_deleted_during_processing(self, db_session, sample_markdown_file):
        """Test conversion when file is deleted during processing"""
        runner = PandocRunner(db_session)

        # Mock the _build_command to delete the file during processing
        original_build_command = runner._build_command
        
        async def mock_build_command(*args, **kwargs):
            # Delete the file during command building
            sample_markdown_file.unlink()
            return await original_build_command(*args, **kwargs)

        runner._build_command = mock_build_command

        with pytest.raises(FileProcessingError) as exc_info:
            await runner.convert(
                input_file=sample_markdown_file,
                from_format="markdown",
                to_format="html"
            )

        assert "was deleted before Pandoc execution" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_pandoc_timeout(self, runner_with_temp_dir):
        """Test Pandoc execution timeout raises PandocConversionError"""
        runner = runner_with_temp_dir

        with patch('app.core.pandoc_runner.asyncio.wait_for') as mock_wait_for:
            mock_wait_for.side_effect = asyncio.TimeoutError()

            with pytest.raises(PandocConversionError) as exc_info:
                await runner._execute_pandoc(["pandoc", "--version"])

            assert "timed out" in str(exc_info.value)
            assert "timeout" in exc_info.value.details

    @pytest.mark.asyncio
    async def test_execute_pandoc_failure(self, runner_with_temp_dir):
        """Test Pandoc execution failure raises PandocConversionError"""
        runner = runner_with_temp_dir

        # Mock a failed process
        mock_process = Mock()
        mock_process.returncode = 1
        mock_process.communicate = AsyncMock(return_value=(b"", b"Error: Invalid format"))

        with patch('app.core.pandoc_runner.asyncio.create_subprocess_exec') as mock_create:
            mock_create.return_value = mock_process

            with pytest.raises(PandocConversionError) as exc_info:
                await runner._execute_pandoc(["pandoc", "--invalid-option"])

            assert "execution failed" in str(exc_info.value)
            assert "Invalid format" in str(exc_info.value)
            assert exc_info.value.details["return_code"] == 1

    @pytest.mark.asyncio
    async def test_execute_pandoc_subprocess_error(self, runner_with_temp_dir):
        """Test subprocess creation error raises PandocConversionError"""
        runner = runner_with_temp_dir

        with patch('app.core.pandoc_runner.asyncio.create_subprocess_exec') as mock_create:
            mock_create.side_effect = OSError("Command not found")

            with pytest.raises(PandocConversionError) as exc_info:
                await runner._execute_pandoc(["nonexistent-command"])

            assert "Failed to execute Pandoc" in str(exc_info.value)
            assert "Command not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_resource_path_template_not_found(self, db_session):
        """Test getting nonexistent template raises TemplateNotFoundError"""
        runner = PandocRunner(db_session)

        with pytest.raises(TemplateNotFoundError) as exc_info:
            await runner._get_resource_path("nonexistent", ResourceType.DOCX_TEMPLATE)

        assert "nonexistent" in str(exc_info.value)
        assert "not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_resource_path_file_not_exists(self, db_session):
        """Test getting template with missing file raises TemplateNotFoundError"""
        # Create a template resource with a nonexistent file
        template = TemplateResource(
            name="test_template",
            display_name="Test Template",
            resource_type=ResourceType.DOCX_TEMPLATE.value,
            file_path="/nonexistent/path.docx",
            file_size=100,
            is_active=True,
        )
        db_session.add(template)
        db_session.commit()

        runner = PandocRunner(db_session)

        with pytest.raises(TemplateNotFoundError) as exc_info:
            await runner._get_resource_path("test_template", ResourceType.DOCX_TEMPLATE)

        assert "test_template" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_build_command_with_missing_templates_logs_warning(self, db_session, sample_markdown_file):
        """Test that missing templates log warnings but don't raise exceptions"""
        runner = PandocRunner(db_session)
        runner.temp_dir = Path(tempfile.mkdtemp())

        try:
            with patch('app.core.pandoc_runner.logger') as mock_logger:
                cmd = await runner._build_command(
                    input_file=sample_markdown_file,
                    from_format="markdown",
                    to_format="html",
                    docx_template="nonexistent_template",
                    csl_style="nonexistent_style",
                    options=None,
                    metadata=None,
                    output_filename=None,
                )

                # Should have logged warnings for missing templates
                assert mock_logger.warning.call_count == 2
                warning_calls = [call.args[0] for call in mock_logger.warning.call_args_list]
                assert any("nonexistent_template" in call for call in warning_calls)
                assert any("nonexistent_style" in call for call in warning_calls)

                # Command should still be built successfully
                assert "pandoc" in cmd
                assert str(sample_markdown_file) in cmd

        finally:
            if runner.temp_dir and runner.temp_dir.exists():
                import shutil
                shutil.rmtree(runner.temp_dir)

    @pytest.mark.asyncio
    async def test_convert_success_returns_files(self, db_session, sample_markdown_file):
        """Test successful conversion returns list of output files"""
        runner = PandocRunner(db_session)

        # Mock successful execution and file operations
        with patch.object(runner, '_execute_pandoc') as mock_execute, \
             patch.object(runner, '_find_output_files') as mock_find, \
             patch.object(runner, '_move_output_files') as mock_move:
            
            mock_find.return_value = [Path("/tmp/output.html")]
            mock_move.return_value = [Path("/final/output.html")]

            result = await runner.convert(
                input_file=sample_markdown_file,
                from_format="markdown",
                to_format="html"
            )

            assert isinstance(result, list)
            assert len(result) == 1
            assert result[0] == Path("/final/output.html")
            mock_execute.assert_called_once()
            mock_find.assert_called_once()
            mock_move.assert_called_once()
