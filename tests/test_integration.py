"""Integration tests for the full conversion pipeline"""

import tempfile
import zipfile
from pathlib import Path
from unittest.mock import patch, Mo<PERSON>
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.database import Base, get_db
from app.models.template import TemplateResource, ResourceType
from app.core.config import settings


# Test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_integration.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="module")
def setup_database():
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client(setup_database):
    return TestClient(app)


@pytest.fixture
def sample_markdown_content():
    return """# Test Document

This is a **test** document with *emphasis*.

## Features

- Item 1
- Item 2
- Item 3

### Code Example

```python
def hello_world():
    print("Hello, World!")
```

### Math

The formula is: $E = mc^2$

### Table

| Name | Age | City |
|------|-----|------|
| John | 25  | NYC  |
| Jane | 30  | LA   |
"""


@pytest.fixture
def sample_zip_file(sample_markdown_content):
    """Create a ZIP file with markdown content"""
    with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as zip_file:
        zip_path = Path(zip_file.name)

    with zipfile.ZipFile(zip_path, 'w') as zipf:
        zipf.writestr("README.md", sample_markdown_content)
        zipf.writestr("assets/image.png", b"fake image data")
        zipf.writestr("docs/guide.md", "# Guide\n\nThis is a guide.")

    yield zip_path

    # Cleanup
    if zip_path.exists():
        zip_path.unlink()


class TestIntegrationConversion:
    """Integration tests for document conversion"""

    @pytest.mark.integration
    def test_convert_markdown_to_html_success(self, client, sample_markdown_content):
        """Test successful markdown to HTML conversion"""
        # Mock Pandoc execution to avoid requiring actual Pandoc installation
        with patch('app.core.pandoc_runner.asyncio.create_subprocess_exec') as mock_create:
            # Mock successful Pandoc process
            mock_process = Mock()
            mock_process.returncode = 0
            mock_process.communicate.return_value = (b"", b"")
            mock_create.return_value = mock_process

            # Mock file operations
            with patch('app.core.pandoc_runner.PandocRunner._find_output_files') as mock_find, \
                 patch('app.core.pandoc_runner.PandocRunner._move_output_files') as mock_move, \
                 patch('app.utils.file_handler.FileHandler.record_output_file') as mock_record:
                
                # Setup mocks
                output_file = Path("/tmp/output.html")
                mock_find.return_value = [output_file]
                mock_move.return_value = [output_file]
                mock_record.return_value = Mock(
                    filename="output.html",
                    file_size=1024,
                    mime_type="text/html",
                    created_at="2023-01-01T00:00:00",
                    expires_at="2023-01-02T00:00:00"
                )

                # Create temporary file for upload
                with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as f:
                    f.write(sample_markdown_content)
                    temp_path = Path(f.name)

                try:
                    # Test conversion
                    with open(temp_path, "rb") as file:
                        response = client.post(
                            "/api/v1/convert",
                            files={"file": ("test.md", file, "text/markdown")},
                            data={
                                "from_format": "markdown",
                                "to_format": "html",
                            }
                        )

                    assert response.status_code == 200
                    data = response.json()
                    assert data["success"] is True
                    assert "output.html" in data["output_files"]
                    assert data["download_url"] is not None
                    assert data["conversion_time"] > 0

                finally:
                    if temp_path.exists():
                        temp_path.unlink()

    @pytest.mark.integration
    def test_convert_zip_file_success(self, client, sample_zip_file):
        """Test successful ZIP file conversion"""
        with patch('app.core.pandoc_runner.asyncio.create_subprocess_exec') as mock_create:
            # Mock successful Pandoc process
            mock_process = Mock()
            mock_process.returncode = 0
            mock_process.communicate.return_value = (b"", b"")
            mock_create.return_value = mock_process

            # Mock file operations
            with patch('app.core.pandoc_runner.PandocRunner._find_output_files') as mock_find, \
                 patch('app.core.pandoc_runner.PandocRunner._move_output_files') as mock_move, \
                 patch('app.utils.file_handler.FileHandler.record_output_file') as mock_record:
                
                # Setup mocks
                output_file = Path("/tmp/output.html")
                mock_find.return_value = [output_file]
                mock_move.return_value = [output_file]
                mock_record.return_value = Mock(
                    filename="output.html",
                    file_size=1024,
                    mime_type="text/html",
                    created_at="2023-01-01T00:00:00",
                    expires_at="2023-01-02T00:00:00"
                )

                # Test ZIP conversion
                with open(sample_zip_file, "rb") as file:
                    response = client.post(
                        "/api/v1/convert",
                        files={"file": ("test.zip", file, "application/zip")},
                        data={
                            "from_format": "markdown",
                            "to_format": "html",
                        }
                    )

                assert response.status_code == 200
                data = response.json()
                assert data["success"] is True
                assert data["conversion_time"] > 0

    @pytest.mark.integration
    def test_convert_with_invalid_file_format(self, client):
        """Test conversion with invalid file format returns error"""
        # Create a file with invalid content
        with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False) as f:
            f.write("This is not valid markdown or any supported format")
            temp_path = Path(f.name)

        try:
            with patch('app.core.pandoc_runner.asyncio.create_subprocess_exec') as mock_create:
                # Mock failed Pandoc process
                mock_process = Mock()
                mock_process.returncode = 1
                mock_process.communicate.return_value = (b"", b"Error: Unknown format")
                mock_create.return_value = mock_process

                with open(temp_path, "rb") as file:
                    response = client.post(
                        "/api/v1/convert",
                        files={"file": ("test.txt", file, "text/plain")},
                        data={
                            "from_format": "invalid_format",
                            "to_format": "html",
                        }
                    )

                assert response.status_code == 422
                data = response.json()
                assert "error" in data or "detail" in data

        finally:
            if temp_path.exists():
                temp_path.unlink()

    @pytest.mark.integration
    def test_convert_with_template(self, client, sample_markdown_content):
        """Test conversion with template"""
        # Create a template in the database
        db = next(override_get_db())
        
        # Create a temporary template file
        with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as template_file:
            template_file.write(b"fake docx template content")
            template_path = Path(template_file.name)

        try:
            template = TemplateResource(
                name="test_template",
                display_name="Test Template",
                resource_type=ResourceType.DOCX_TEMPLATE.value,
                file_path=str(template_path),
                file_size=template_path.stat().st_size,
                is_active=True,
            )
            db.add(template)
            db.commit()

            with patch('app.core.pandoc_runner.asyncio.create_subprocess_exec') as mock_create:
                # Mock successful Pandoc process
                mock_process = Mock()
                mock_process.returncode = 0
                mock_process.communicate.return_value = (b"", b"")
                mock_create.return_value = mock_process

                # Mock file operations
                with patch('app.core.pandoc_runner.PandocRunner._find_output_files') as mock_find, \
                     patch('app.core.pandoc_runner.PandocRunner._move_output_files') as mock_move, \
                     patch('app.utils.file_handler.FileHandler.record_output_file') as mock_record:
                    
                    # Setup mocks
                    output_file = Path("/tmp/output.docx")
                    mock_find.return_value = [output_file]
                    mock_move.return_value = [output_file]
                    mock_record.return_value = Mock(
                        filename="output.docx",
                        file_size=2048,
                        mime_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        created_at="2023-01-01T00:00:00",
                        expires_at="2023-01-02T00:00:00"
                    )

                    # Create temporary file for upload
                    with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as f:
                        f.write(sample_markdown_content)
                        input_path = Path(f.name)

                    try:
                        with open(input_path, "rb") as file:
                            response = client.post(
                                "/api/v1/convert",
                                files={"file": ("test.md", file, "text/markdown")},
                                data={
                                    "from_format": "markdown",
                                    "to_format": "docx",
                                    "docx_template": "test_template",
                                }
                            )

                        assert response.status_code == 200
                        data = response.json()
                        assert data["success"] is True
                        assert "output.docx" in data["output_files"]

                    finally:
                        if input_path.exists():
                            input_path.unlink()

        finally:
            # Cleanup
            if template_path.exists():
                template_path.unlink()
            db.close()

    @pytest.mark.integration
    def test_health_check_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "message" in data

    @pytest.mark.integration
    def test_supported_formats_endpoint(self, client):
        """Test supported formats endpoint"""
        response = client.get("/api/v1/formats")
        assert response.status_code == 200
        data = response.json()
        assert "input_formats" in data
        assert "output_formats" in data
        assert isinstance(data["input_formats"], list)
        assert isinstance(data["output_formats"], list)
