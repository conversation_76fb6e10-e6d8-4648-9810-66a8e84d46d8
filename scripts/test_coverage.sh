#!/bin/bash

# Test coverage script for pandoc-fastapi
# This script runs tests with coverage reporting

set -e

echo "Running tests with coverage..."

# Activate virtual environment if it exists
if [ -d ".venv" ]; then
    source .venv/bin/activate
fi

# Run tests with coverage
coverage run -m pytest tests/ -v

# Generate coverage reports
echo "Generating coverage reports..."
coverage report --show-missing
coverage html
coverage xml

echo "Coverage reports generated:"
echo "  - Terminal: coverage report --show-missing"
echo "  - HTML: htmlcov/index.html"
echo "  - XML: coverage.xml"

# Check coverage threshold
echo "Checking coverage threshold..."
coverage report --fail-under=60

echo "✅ All tests passed with sufficient coverage!"
